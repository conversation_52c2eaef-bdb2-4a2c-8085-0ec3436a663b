import request from "@/utils/request";

// 规则链相关接口
export function getRuleChainList(data) {
  return request({
    url: "/chain/page",
    method: "post",
    data: data,
  });
}

export function updateRuleChainEnable(data) {
  return request({
    url: "/chain/updateEnable",
    method: "get",
    params: data,
  });
}

export function addRuleChain(data) {
  return request({
    url: "/chain/insert",
    method: "post",
    data: data,
  });
}

export function updateRule<PERSON>hain(data) {
  return request({
    url: "/chain/update",
    method: "post",
    data: data,
  });
}

export function getRuleChainDetail(data) {
  return request({
    url: "/chain/detail",
    method: "get",
    params: data,
  });
}

export function deleteRuleChain(data) {
  return request({
    url: "/chain/delete",
    method: "get",
    params: data,
  });
}

export function generateLogicFlowEL(data) {
  return request({
    url: "/chain/generateLogicFlowEL",
    method: "post",
    data: data,
  });
}

// 调试相关接口
export function queryNodeDataList(data) {
  return request({
    url: "/chain/queryNodeDataList",
    method: "get",
    params: data,
  });
}

export function chainInitialContext(data) {
  return request({
    url: "/chain/chainInitialContext",
    method: "post",
    data: data,
  });
}

export function nodeSelfTest(data) {
  return request({
    url: "/chain/nodeSelfTest",
    method: "post",
    data: data,
  });
}

export function queryChainInstanceList(data) {
  return request({
    url: "/chain/queryChainInstanceList",
    method: "get",
    params: data,
  });
}

export function queryChainList(data) {
  return request({
    url: "/chain/queryChainList",
    method: "get",
    params: data,
  });
}

// 版本控制接口
export function getVersionList(data) {
  return request({
    url: "/chain/version/list",
    method: "get",
    params: data,
  });
}

export function getVersionDetail(data) {
  return request({
    url: "/chain/version/detail",
    method: "get",
    params: data,
  });
}

export function compareVersion(data) {
  return request({
    url: "/chain/version/compare",
    method: "post",
    data: data,
  });
}

export function rollbackVersion(data) {
  return request({
    url: "/chain/version/rollback",
    method: "post",
    data: data,
  });
}

export default {
  getRuleChainList,
  updateRuleChainEnable,
  addRuleChain,
  updateRuleChain,
  getRuleChainDetail,
  deleteRuleChain,
  generateLogicFlowEL,
  queryNodeDataList,
  chainInitialContext,
  nodeSelfTest,
  queryChainInstanceList,
  queryChainList,
  getVersionList,
  getVersionDetail,
  compareVersion,
  rollbackVersion,
};
