<template>
  <el-dialog
    :title="title"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form
      ref="form"
      :model="localFormData"
      :rules="rules"
      label-width="100px"
    >
      <el-form-item label="规则链名称" prop="chainName">
        <el-input
          v-model="localFormData.chainName"
          placeholder="请输入规则链名称"
        />
      </el-form-item>

      <el-form-item label="描述" prop="description">
        <el-input
          v-model="localFormData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入描述"
        />
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-select v-model="localFormData.status" placeholder="请选择状态">
          <el-option label="启用" value="1" />
          <el-option label="停用" value="0" />
        </el-select>
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: "RuleChainModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    formData: {
      type: Object,
      default: () => ({}),
    },
    operationType: {
      type: String,
      default: "add",
    },
  },
  data() {
    return {
      loading: false,
      localFormData: {
        chainName: "",
        description: "",
        status: "1",
      },
      rules: {
        chainName: [
          { required: true, message: "请输入规则链名称", trigger: "blur" },
        ],
      },
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
    title() {
      return this.operationType === "add" ? "新增规则链" : "编辑规则链";
    },
  },
  watch: {
    formData: {
      handler(newVal) {
        this.localFormData = {
          chainName: newVal.chainName || "",
          description: newVal.description || "",
          status: newVal.status || "1",
        };
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.loading = true;
          // 模拟API调用
          setTimeout(() => {
            this.$emit("confirm", this.localFormData);
            this.loading = false;
            this.handleClose();
          }, 1000);
        }
      });
    },

    handleClose() {
      this.$refs.form.resetFields();
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="less" scoped>
.dialog-footer {
  text-align: right;
}
</style>
