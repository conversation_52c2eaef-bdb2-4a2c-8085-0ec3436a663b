<template>
  <div class="expression-preview">
    <div class="expression-header">
      <h4>表达式预览</h4>
      <el-button size="mini" @click="handleRefresh" icon="el-icon-refresh"
        >刷新</el-button
      >
    </div>
    <div class="expression-content">
      <el-input
        type="textarea"
        :value="expression"
        readonly
        :rows="6"
        placeholder="暂无表达式"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: "ExpressionPreview",
  props: {
    expression: {
      type: String,
      default: "",
    },
  },
  methods: {
    handleRefresh() {
      this.$emit("refresh");
    },
  },
};
</script>

<style lang="less" scoped>
.expression-preview {
  position: absolute;
  bottom: 24px;
  right: 24px;
  width: 400px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;

  .expression-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .expression-content {
    padding: 16px;

    /deep/ .el-textarea__inner {
      font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
      font-size: 12px;
      line-height: 1.4;
    }
  }
}
</style>
