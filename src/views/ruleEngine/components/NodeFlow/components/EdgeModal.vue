<template>
  <el-dialog
    title="连线配置"
    :visible.sync="dialogVisible"
    width="500px"
    @close="handleClose"
  >
    <el-form :model="formData" label-width="100px">
      <el-form-item label="连线类型">
        <el-select v-model="formData.edgeType" placeholder="请选择连线类型">
          <el-option
            v-for="tag in edgeTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入连线描述"
        />
      </el-form-item>
    </el-form>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getElNodeConfigByType } from "../config/nodeTypes";

export default {
  name: "EdgeModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    edgeData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        edgeType: "",
        description: "",
      },
      edgeTags: [],
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    edgeData: {
      handler(newData) {
        if (newData && newData.sourceNode) {
          this.initFormData(newData);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    initFormData(edgeData) {
      const { sourceNode, properties = {} } = edgeData;

      // 获取源节点的边标签
      const nodeConfig = getElNodeConfigByType(sourceNode.properties?.nodeType);
      this.edgeTags = nodeConfig?.edgeTags || [];

      this.formData = {
        edgeType: properties.edgeType || "",
        description: properties.description || "",
      };
    },

    handleSave() {
      const saveData = {
        ...this.edgeData,
        properties: {
          ...this.edgeData.properties,
          ...this.formData,
        },
      };

      this.$emit("save", saveData);
      this.handleClose();
    },

    handleClose() {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="less" scoped>
.dialog-footer {
  text-align: right;
}
</style>
