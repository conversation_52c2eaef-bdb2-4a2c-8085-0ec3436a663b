<template>
  <el-dialog
    title="版本控制"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <div class="version-content">
      <p>规则链: {{ chainName }}</p>
      <p>当前版本: {{ currentVersionData.version || "1.0" }}</p>

      <el-table :data="versionList" style="width: 100%">
        <el-table-column prop="version" label="版本号" width="120" />
        <el-table-column prop="createTime" label="创建时间" width="180" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="操作" width="120">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleRestore(scope.row)"
              >恢复</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getVersionList, rollbackVersion } from "@/api/ruleEngine/index.js";

export default {
  name: "VersionModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    chainId: String,
    chainName: String,
    currentVersionData: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      versionList: [],
      loading: false,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  watch: {
    visible(newVal) {
      if (newVal && this.chainId) {
        this.loadVersionList();
      }
    },
  },
  methods: {
    // 加载版本列表
    async loadVersionList() {
      if (!this.chainId) return;
      this.loading = true;
      try {
        const res = await getVersionList({
          chainId: this.chainId,
          pageCount: 1,
          pageSize: 50, // 加载更多版本
        });

        if (res?.success) {
          this.versionList = res.data || [];
        }
      } catch (error) {
        // 错误已在拦截器中处理
      } finally {
        this.loading = false;
      }
    },

    // 恢复版本
    async handleRestore(record) {
      this.$confirm({
        title: "确认恢复",
        content: `确定要恢复到版本 ${record.version} 吗？`,
        okText: "确定",
        cancelText: "取消",
        onOk: async () => {
          this.loading = true;
          try {
            const res = await rollbackVersion({
              chainId: this.chainId,
              version: record.version,
            });

            if (res?.success) {
              this.$message.success("版本恢复成功");
              this.$emit("restore", record);
              this.handleClose();
            }
          } catch (error) {
            // 错误已在拦截器中处理
          } finally {
            this.loading = false;
          }
        },
      });
    },

    handleClose() {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="less" scoped>
.version-content {
  p {
    margin: 8px 0;
    color: #666;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
