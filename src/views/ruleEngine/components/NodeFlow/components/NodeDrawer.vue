<template>
  <el-drawer
    title="节点配置"
    :visible.sync="drawerVisible"
    direction="rtl"
    size="500px"
    @close="handleClose"
  >
    <div class="node-drawer-content">
      <el-form :model="formData" label-width="100px">
        <el-form-item label="节点名称">
          <el-input v-model="formData.name" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="节点描述">
          <el-input
            v-model="formData.description"
            type="textarea"
            :rows="3"
            placeholder="请输入节点描述"
          />
        </el-form-item>
        <el-form-item label="节点类型">
          <el-input
            :value="nodeData.properties && nodeData.properties.nodeType"
            readonly
          />
        </el-form-item>
      </el-form>
    </div>

    <div class="drawer-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSave">保存</el-button>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: "NodeDrawer",
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    nodeData: {
      type: Object,
      default: () => ({}),
    },
    isDebug: {
      type: Boolean,
      default: false,
    },
    chainId: String,
    chainInstanceId: String,
    chainName: String,
  },
  data() {
    return {
      formData: {
        name: "",
        description: "",
      },
    };
  },
  computed: {
    drawerVisible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    nodeData: {
      handler(newData) {
        if (newData && newData.properties) {
          this.formData = {
            name: newData.properties.name || newData.text?.value || "",
            description: newData.properties.description || "",
          };
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    handleSave() {
      this.$emit("save", this.formData);
    },
    handleClose() {
      this.$emit("close");
    },
  },
};
</script>

<style lang="less" scoped>
.node-drawer-content {
  padding: 20px;
  height: calc(100% - 60px);
  overflow-y: auto;
}

.drawer-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
  background: #fff;
  text-align: right;
}
</style>
