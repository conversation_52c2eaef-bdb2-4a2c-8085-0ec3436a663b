<template>
  <el-dialog
    title="节点自测"
    :visible.sync="dialogVisible"
    width="600px"
    @close="handleClose"
  >
    <div class="test-content">
      <el-form label-width="100px">
        <el-form-item label="测试数据">
          <el-input
            type="textarea"
            :rows="4"
            v-model="testData"
            placeholder="请输入测试数据（JSON格式）"
          />
        </el-form-item>
        <el-form-item label="执行结果">
          <el-input
            type="textarea"
            :rows="6"
            v-model="testResult"
            readonly
            placeholder="测试结果将显示在这里"
          />
        </el-form-item>
      </el-form>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleClose">关闭</el-button>
      <el-button type="primary" @click="handleTest">执行测试</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { nodeSelfTest } from "@/api/ruleEngine/index.js";

export default {
  name: "TestModal",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    selectionInfo: {
      type: Object,
      default: () => ({}),
    },
    chainInstanceId: String,
  },
  data() {
    return {
      testData: '{\n  "input": "test data"\n}',
      testResult: "",
      loading: false,
    };
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  methods: {
    async handleTest() {
      try {
        // 解析测试数据
        const inputData = JSON.parse(this.testData);

        // 检查是否有选中的节点
        if (!this.selectionInfo || !this.selectionInfo.id) {
          this.$message.warning("请先选择一个节点");
          return;
        }

        this.loading = true;

        // 调用节点自测API
        const res = await nodeSelfTest({
          nodeId: this.selectionInfo.id,
          chainId: this.selectionInfo.chainId || "",
          chainInstanceId: this.chainInstanceId || "",
          context: inputData,
        });

        if (res?.success) {
          this.testResult = JSON.stringify(res.data || {}, null, 2);
        } else {
          this.testResult = JSON.stringify(
            {
              success: false,
              message: res?.message || "测试执行失败",
              timestamp: new Date().toISOString(),
            },
            null,
            2
          );
        }
      } catch (error) {
        // 处理JSON解析错误或API调用错误
        this.testResult = JSON.stringify(
          {
            success: false,
            error: error.message || "测试执行失败",
            timestamp: new Date().toISOString(),
          },
          null,
          2
        );
      } finally {
        this.loading = false;
      }
    },
    handleClose() {
      this.$emit("update:visible", false);
    },
  },
};
</script>

<style lang="less" scoped>
.test-content {
  /deep/ .el-textarea__inner {
    font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
    font-size: 12px;
  }
}

.dialog-footer {
  text-align: right;
}
</style>
