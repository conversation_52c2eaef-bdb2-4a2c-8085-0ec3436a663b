<template>
  <div class="node-panel">
    <div class="node-panel-header">
      <el-tooltip :content="chainName" placement="top">
        <span class="chain-name">{{ chainName || "未命名" }}</span>
      </el-tooltip>
    </div>
    <el-collapse v-model="activeKeys">
      <el-collapse-item
        v-for="(nodes, type) in nodeTypes"
        :key="type"
        :title="getPanelHeader(type)"
        :name="type"
      >
        <el-tooltip
          v-for="(item, index) in nodes"
          :key="`${type}-${item.type}-${index}`"
          :content="item.label"
          placement="top"
        >
          <div
            class="node-item"
            @mousedown="onDragStart(item)"
            :class="{ 'function-node': type === 'function' }"
            :style="{ borderColor: item.properties.color }"
          >
            <div class="node-item-content">
              <div class="node-item-icon">
                <svg viewBox="0 0 1024 1024" width="20" height="20">
                  <path
                    :d="getIconPath(item.properties.icon)"
                    :fill="item.properties.color"
                  />
                </svg>
              </div>
              <div class="node-item-label">{{ item.label }}</div>
            </div>
          </div>
        </el-tooltip>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import { nodeTypes, typeHeaderMap } from "../config/nodeTypes";
import { nodeIcons } from "../config/icons";

export default {
  name: "NodePanel",
  props: {
    chainName: {
      type: String,
      default: "",
    },
  },
  data() {
    return {
      nodeTypes,
      activeKeys: ["el", "function"],
    };
  },
  methods: {
    onDragStart(item) {
      this.$emit("drag-start", item);
    },
    getPanelHeader(type) {
      return typeHeaderMap[type] || type;
    },
    getIconPath(type) {
      return nodeIcons[type];
    },
  },
};
</script>

<style lang="less" scoped>
.node-panel {
  width: 280px;
  height: 100%;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
  border-radius: 8px;
  margin-right: 24px;

  .node-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    margin-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;

    .chain-name {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }
  }

  /deep/.el-collapse {
    border: none;

    .el-collapse-item__header {
      font-weight: 500;
      color: #333;
      background: transparent;
      border: none;
      padding-left: 0;
    }

    .el-collapse-item__content {
      padding-bottom: 0;
    }
  }

  .node-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    margin-bottom: 8px;
    border: 2px solid #e8e8e8;
    border-radius: 6px;
    cursor: grab;
    transition: all 0.2s ease;
    background: #fff;

    &:hover {
      border-color: #00b099;
      box-shadow: 0 2px 8px rgba(0, 176, 153, 0.15);
      transform: translateY(-1px);
    }

    &:active {
      cursor: grabbing;
      transform: translateY(0);
    }

    &.function-node {
      border-radius: 50px;
    }

    .node-item-content {
      display: flex;
      align-items: center;
      width: 100%;

      .node-item-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 8px;
        flex-shrink: 0;

        svg {
          display: block;
        }
      }

      .node-item-label {
        font-size: 14px;
        color: #333;
        font-weight: 500;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        flex: 1;
      }
    }
  }
}
</style>
