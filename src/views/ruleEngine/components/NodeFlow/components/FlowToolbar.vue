<template>
  <div class="toolbar">
    <div class="toolbar-item">
      <el-button
        :icon="isSelecting ? 'el-icon-close' : 'el-icon-s-operation'"
        :type="isSelecting ? 'default' : 'primary'"
        size="small"
        @click="handleClick"
        class="mr-8"
      >
        {{ isSelecting ? "关闭框选" : "框选" }}
      </el-button>

      <el-button
        :icon="isDebug ? 'el-icon-close' : 'el-icon-cpu'"
        :type="isDebug ? 'default' : 'primary'"
        size="small"
        @click="handleDebug"
        class="mr-8"
      >
        {{ isDebug ? "退出调试" : "调试" }}
      </el-button>

      <el-dropdown @command="handleCommand" class="toolbar-group">
        <el-button type="primary" size="small">
          工具箱<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="version">
            <i class="el-icon-time"></i>
            <span>版本控制</span>
          </el-dropdown-item>
          <el-dropdown-item command="export">
            <i class="el-icon-download"></i>
            <span>导出</span>
          </el-dropdown-item>
          <el-dropdown-item command="import">
            <i class="el-icon-upload2"></i>
            <span>导入</span>
          </el-dropdown-item>
          <el-dropdown-item divided command="toggleExpression">
            <i
              :class="showExpressionPreview ? 'el-icon-view' : 'el-icon-view'"
            ></i>
            <span>{{
              showExpressionPreview ? "隐藏表达式预览" : "显示表达式预览"
            }}</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <input
      type="file"
      ref="fileInput"
      accept=".json"
      style="display: none"
      @change="handleFileSelected"
    />
  </div>
</template>

<script>
export default {
  name: "FlowToolbar",
  props: {
    isSelecting: {
      type: Boolean,
      default: false,
    },
    showExpressionPreview: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      isDebug: false,
    };
  },
  mounted() {
    this.isDebug = localStorage.getItem("isDebug") === "true";
  },
  methods: {
    handleClick() {
      this.$emit("selection-change");
    },
    handleDebug() {
      if (!this.isDebug) {
        this.$emit("debug");
      } else {
        this.$emit("debug-close");
      }
      this.isDebug = !this.isDebug;
    },
    handleCommand(command) {
      switch (command) {
        case "version":
          this.handleVersionControl();
          break;
        case "export":
          this.handleExport();
          break;
        case "import":
          this.handleImport();
          break;
        case "toggleExpression":
          this.handleToggleExpression();
          break;
      }
    },
    handleVersionControl() {
      this.$emit("version-control");
    },
    handleExport() {
      this.$emit("export");
    },
    handleImport() {
      this.$refs.fileInput.click();
    },
    handleToggleExpression() {
      this.$emit("toggle-expression");
    },
    handleFileSelected(event) {
      const file = event.target.files[0];
      if (!file) return;

      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result);
          this.$emit("import", data);
        } catch (error) {
          this.$message.error("文件格式错误，请选择正确的JSON文件");
        }
      };
      reader.readAsText(file);

      // 清空文件输入框
      event.target.value = "";
    },
  },
};
</script>

<style lang="less" scoped>
.toolbar {
  position: absolute;
  top: 24px;
  left: 24px;
  z-index: 1000;

  .toolbar-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .mr-8 {
      margin-right: 8px;
    }

    .toolbar-group {
      margin-left: 8px;
    }
  }
}
</style>
