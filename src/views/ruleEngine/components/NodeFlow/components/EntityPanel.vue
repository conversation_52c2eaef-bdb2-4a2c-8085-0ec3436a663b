<template>
  <div class="entity-panel">
    <div class="entity-panel-header">
      <h3>调试面板</h3>
      <p>规则链: {{ chainName }}</p>
      <p>版本: {{ version }}</p>
    </div>

    <el-tabs v-model="activeTab" type="border-card">
      <!-- 当前版本实例 -->
      <el-tab-pane label="当前版本实例" name="current">
        <div class="tab-content">
          <el-table
            :data="versionData.current.entities"
            :loading="loading"
            size="small"
            height="300"
          >
            <el-table-column
              prop="chainInstanceId"
              label="实例ID"
              width="120"
            />
            <el-table-column prop="status" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag
                  :type="scope.row.status === 'SUCCESS' ? 'success' : 'danger'"
                >
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="selectEntity(scope.row)"
                >
                  选择
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="viewContext(scope.row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            v-if="versionData.current.total > 0"
            :current-page="currentPagination.current"
            :page-size="currentPagination.pageSize"
            :total="versionData.current.total"
            layout="prev, pager, next"
            @current-change="handleCurrentPageChange"
            class="pagination"
          />
        </div>
      </el-tab-pane>

      <!-- 历史版本 -->
      <el-tab-pane label="历史版本" name="history">
        <div class="tab-content">
          <el-table
            :data="versionData.history.versions"
            :loading="loading"
            size="small"
            height="200"
          >
            <el-table-column prop="version" label="版本" width="80" />
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  @click="toggleVersionEntities(scope.row.version)"
                >
                  {{ scope.row.expanded ? "收起" : "展开" }}
                </el-button>
                <el-button
                  type="text"
                  size="mini"
                  @click="handleVersionView(scope.row)"
                >
                  查看
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import {
  queryChainInstanceList,
  getVersionList,
  chainInitialContext,
  getVersionDetail,
} from "@/api/ruleEngine/index.js";

export default {
  name: "EntityPanel",
  props: {
    chainId: String,
    chainName: String,
    version: String,
  },
  data() {
    return {
      activeTab: "current",
      loading: false,
      versionData: {
        current: {
          entities: [],
          total: 0,
        },
        history: {
          versions: [],
          total: 0,
        },
      },
      currentPagination: {
        current: 1,
        pageSize: 10,
      },
      historyVersionPagination: {
        current: 1,
        pageSize: 10,
      },
      historyEntityPagination: {
        current: 1,
        pageSize: 10,
      },
      isViewingHistory: false,
    };
  },
  watch: {
    chainName: {
      handler() {
        this.initData();
      },
      immediate: true,
    },
    activeTab(newTab) {
      if (newTab === "current") {
        this.getCurrentVersionEntities();
      } else if (newTab === "history") {
        this.getHistoryVersions();
      }
    },
  },
  methods: {
    initData() {
      if (this.chainName) {
        this.getCurrentVersionEntities();
      }
    },

    // 获取当前版本的实例列表
    async getCurrentVersionEntities() {
      if (!this.chainId) return;
      this.loading = true;
      try {
        const res = await queryChainInstanceList({
          chainId: this.chainId,
          pageCount: this.currentPagination.current,
          pageSize: this.currentPagination.pageSize,
        });

        if (res?.success) {
          this.versionData.current.entities = res.data || [];
          this.versionData.current.total = res.total || 0;
        }
      } catch (error) {
        // 错误已在拦截器中处理
      } finally {
        this.loading = false;
      }
    },

    // 获取历史版本列表
    async getHistoryVersions() {
      if (!this.chainId) return;
      this.loading = true;
      try {
        const res = await getVersionList({
          chainId: this.chainId,
          pageCount: this.historyVersionPagination.current,
          pageSize: this.historyVersionPagination.pageSize,
        });

        if (res?.success) {
          // 添加展开状态标记
          this.versionData.history.versions = (res.data || []).map((v) => ({
            ...v,
            expanded: false,
            entities: [],
          }));
          this.versionData.history.total = res.total || 0;
        }
      } catch (error) {
        // 错误已在拦截器中处理
      } finally {
        this.loading = false;
      }
    },

    // 切换版本实例展开状态
    async toggleVersionEntities(version) {
      const versionItem = this.versionData.history.versions.find(
        (v) => v.version === version
      );
      if (!versionItem) return;

      // 切换展开状态
      versionItem.expanded = !versionItem.expanded;

      // 如果是展开且没有加载过数据，则加载数据
      if (
        versionItem.expanded &&
        (!versionItem.entities || versionItem.entities.length === 0)
      ) {
        this.loading = true;
        try {
          const res = await queryChainInstanceList({
            chainId: this.chainId,
            version: version,
            pageCount: 1,
            pageSize: 10,
          });

          if (res?.success) {
            versionItem.entities = res.data || [];
          }
        } catch (error) {
          // 错误已在拦截器中处理
        } finally {
          this.loading = false;
        }
      }
    },

    // 选择实例
    selectEntity(entity) {
      this.$emit("selected-chain-instance-id", entity.chainInstanceId);
    },

    // 查看实例上下文
    async viewContext(entity) {
      this.loading = true;
      try {
        const res = await chainInitialContext({
          chainInstanceId: entity.chainInstanceId,
        });

        if (res?.success) {
          // 显示上下文数据
          this.$alert(JSON.stringify(res.data, null, 2), "实例上下文", {
            dangerouslyUseHTMLString: true,
            closeOnClickModal: true,
            customClass: "context-dialog",
          });
        }
      } catch (error) {
        // 错误已在拦截器中处理
      } finally {
        this.loading = false;
      }
    },

    // 查看历史版本
    async handleVersionView(versionItem) {
      this.loading = true;
      try {
        const res = await getVersionDetail({
          chainId: this.chainId,
          version: versionItem.version,
        });

        if (res?.success) {
          this.$emit("version-view", {
            isHistoryVersion: true,
            versionData: res.data,
          });
        }
      } catch (error) {
        // 错误已在拦截器中处理
      } finally {
        this.loading = false;
      }
    },

    // 当前页变化
    handleCurrentPageChange(page) {
      this.currentPagination.current = page;
      this.getCurrentVersionEntities();
    },
  },
};
</script>

<style lang="less" scoped>
.entity-panel {
  width: 380px;
  height: 100%;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  background: #fff;
  border-radius: 8px;
  margin-right: 24px;
  display: flex;
  flex-direction: column;

  .entity-panel-header {
    margin-bottom: 16px;

    h3 {
      margin: 0 0 8px 0;
      font-size: 16px;
      font-weight: 500;
    }

    p {
      margin: 4px 0;
      color: #666;
      font-size: 14px;
    }
  }

  .tab-content {
    padding: 8px 0;
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .pagination {
    margin-top: 16px;
    text-align: center;
  }
}

// 上下文对话框样式
:global(.context-dialog) {
  width: 80%;
  max-width: 800px;

  .el-dialog__body {
    max-height: 500px;
    overflow-y: auto;
    white-space: pre-wrap;
    font-family: monospace;
  }
}
</style>
