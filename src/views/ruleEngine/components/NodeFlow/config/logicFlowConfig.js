import LogicFlow from "@logicflow/core";
import { SelectionSelect, DndPanel, Control, Menu } from "@logicflow/extension";
import "@logicflow/core/dist/style/index.css";
import "@logicflow/extension/lib/style/index.css";
import CustomNode from "./CustomNode";
import SubChainNode from "./SubChainNode";
import FunctionNode from "./FunctionNode";
import StartNode from "./StartNode";
import { getElNodeConfigByType, nodeTypes } from "./nodeTypes";
import { getDefaultFormData } from "../utils/defaultFormData";
import _ from "lodash";
import { findNearestNodeByType } from "../utils/handleNode";

// 不需要打开详情的节点
const noDetailNodes = ["PAR", "END"];

export function getLogicFlowConfig(container, context) {
  LogicFlow.use(SelectionSelect);
  LogicFlow.use(DndPanel);
  LogicFlow.use(Control);
  LogicFlow.use(Menu);

  const lf = new LogicFlow({
    container,
    grid: true,
    nodeTextEdit: false,
    edgeTextEdit: false,
    nodeTextDraggable: false,
    edgeTextDraggable: false,
    stopScrollGraph: false,
    stopZoomGraph: false,
    edgeType: "bezier",
    style: {
      rect: {
        radius: 5,
        stroke: "#00b099",
        fill: "#fff",
        strokeWidth: 2,
      },
      circle: {
        r: 25,
        stroke: "#00b099",
        fill: "#fff",
        strokeWidth: 2,
      },
      edge: {
        stroke: "#00b099",
        strokeWidth: 2,
        selectedStroke: "#00b099",
        selectedStrokeWidth: 4,
        edgeAnimation: true,
        edgeAdjust: true,
        textWidth: 100,
        offset: 20,
        strokeDasharray: "",
        text: {
          fontSize: 12,
          color: "#666",
          background: {
            fill: "#fff",
            stroke: "#e2e2e2",
            radius: 2,
          },
          position: "center",
        },
      },
      anchor: {
        stroke: "#00b099",
        fill: "#fff",
        r: 4,
      },
    },
    keyboard: {
      enabled: true,
      shortcuts: [
        {
          keys: [
            "delete",
            "backspace",
            "cmd+backspace",
            "ctrl+backspace",
            "enter",
            "cmd+enter",
            "ctrl+enter",
          ],
          callback: () => {
            const elements = lf.getSelectElements();
            // 如果没有选中任何元素则直接返回
            if (!elements.nodes.length && !elements.edges.length) return;

            // 检查是否包含开始节点
            const hasStartNode = elements.nodes.some(
              (node) => node.type === "start-node"
            );
            if (hasStartNode) {
              lf.emit("node:delete", { data: { type: "start-node" } });
              return;
            }

            // 弹框确认是否删除 - 适配Element UI
            context
              .$confirm("确定要删除选中的节点吗？", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              })
              .then(() => {
                // 删除选中的节点和边
                elements.nodes.forEach((node) => lf.deleteNode(node.id));
                elements.edges.forEach((edge) => lf.deleteEdge(edge.id));
              })
              .catch(() => {
                // 用户取消删除
              });
          },
        },
      ],
    },
  });

  // 注册自定义节点
  lf.register(CustomNode);
  lf.register(StartNode);
  lf.register(FunctionNode);
  lf.register(SubChainNode);

  // 配置普通节点的菜单
  lf.extension.menu.setMenuByType({
    type: "custom-node",
    menu: [
      {
        text: "删除",
        callback(node) {
          context
            .$confirm("确定要删除该节点吗？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(() => {
              lf.deleteNode(node.id);
            })
            .catch(() => {
              // 用户取消删除
            });
        },
      },
      {
        text: "自测",
        callback(node) {
          if (!context.isDebug) {
            context.$message.warning("请先进入调试模式才能进行自测");
            return;
          }
          context.showTestModal(node);
        },
      },
    ],
  });

  lf.extension.menu.setMenuByType({
    type: "sub-chain-node",
    menu: [
      {
        text: "删除",
        callback(node) {
          context
            .$confirm("确定要删除该节点吗？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning",
            })
            .then(() => {
              lf.deleteNode(node.id);
            })
            .catch(() => {
              // 用户取消删除
            });
        },
      },
      {
        text: "跳转子规则链",
        callback(node) {
          const { subChainId } = node.properties;
          context.$emit("jumpToSubChain", subChainId);
        },
      },
    ],
  });

  // 开始节点不设置菜单
  lf.extension.menu.setMenuByType({
    type: "start-node",
    menu: [],
  });

  // 配置自定义节点菜单
  lf.extension.menu.setMenuByType({
    type: "lf:defaultSelectionMenu",
    menu: [
      {
        text: "自测",
        callback(node) {
          if (!context.isDebug) {
            context.$message.warning("请先进入调试模式才能进行自测");
            return;
          }
          context.showTestModal(node);
        },
      },
    ],
  });

  // 获取节点数据
  const getFlowData = () => {
    const flowData = lf.getGraphData();
    return {
      ...flowData,
      nodes: flowData.nodes.map((node) => ({
        ...node,
        properties: {
          ...node.properties,
          name: node.text?.value || node.properties?.name || "",
        },
      })),
    };
  };

  // 处理边线事件的辅助函数
  const handleEdgeEvent = (data) => {
    const sourceNode = lf.getNodeModelById(data.sourceNodeId).getData();
    const targetNode = lf.getNodeModelById(data.targetNodeId).getData();
    const currentEdge = lf.getEdgeModelById(data.id).getData();

    if (sourceNode.type === "start-node") {
      context.$message.warning("开始节点不支持连线配置");
      return;
    }
    const edgeTags =
      getElNodeConfigByType(sourceNode.properties?.nodeType)?.edgeTags || [];

    if (!edgeTags.length) {
      context.$message.warning("该节点类型不支持连线配置");
      return;
    }
    context.currentEdge = {
      ...currentEdge,
      sourceNode: sourceNode,
      targetNode: targetNode,
    };
    context.edgeDrawerVisible = true;
  };

  // 配置事件监听
  const setupEventListeners = () => {
    // 更新表达式的事件
    const updateEvents = ["node:delete", "edge:add", "edge:delete"];

    updateEvents.forEach((eventName) => {
      lf.on(eventName, () => {
        context.getLatestExpression();
      });
    });

    // 节点添加事件
    lf.on("node:add", ({ data }) => {
      const defaultProps = getDefaultFormData(data.properties?.nodeType);
      lf.setProperties(data.id, {
        ...data.properties,
        ...defaultProps,
      });
      context.currentNode = lf.getNodeModelById(data.id).getData();
      if (!noDetailNodes.includes(data.properties?.nodeType)) {
        context.drawerVisible = true;
      }
    });

    // 节点双击事件
    lf.on("node:dbclick", ({ data }) => {
      if (data.type === "start-node") {
        context.$message.warning("开始节点不可编辑");
        return;
      }
      context.currentNode = data;
      if (!noDetailNodes.includes(data.properties?.nodeType)) {
        context.drawerVisible = true;
      }
    });

    // 自测回调事件
    lf.on("node:test", ({ data }) => {
      if (data.type === "start-node") {
        context.$message.warning("开始节点不支持自测");
        return;
      }
      if (!context.isDebug) {
        context.$message.warning("请先进入调试模式才能进行自测");
        return;
      }
      context.currentNode = data;
      context.testModalVisible = true;
    });

    // 连线成功事件
    lf.on("edge:add", (data) => {
      const sourceNode = lf.getNodeModelById(data.data.sourceNodeId).getData();
      const targetNode = lf.getNodeModelById(data.data.targetNodeId).getData();
      const sourceNodeType = sourceNode.properties?.nodeType;
      const targetNodeType = targetNode.properties?.nodeType;

      // 超时节点不能有后续连线
      if (["TIMEOUT"].includes(sourceNodeType)) {
        lf.deleteEdge(data.data.id);
        context.$message.warning(
          `${sourceNodeType === "TIMEOUT" ? "超时" : "结束"}节点不能有后续连线`
        );
        return;
      }

      // 如果是普通节点且已有出边，删除新添加的边
      if (
        !["PAR", "IF", "SWITCH", "FOR", "WHILE", "ITERATOR"].includes(
          sourceNodeType
        )
      ) {
        const nodeEdges = lf.getNodeEdges(sourceNode.id);
        // 过滤出当前节点的出边，但排除目标节点为超时节点的边
        const outgoingEdges = nodeEdges.filter((edge) => {
          if (edge.sourceNodeId !== sourceNode.id) return false;
          const targetNode = lf.getNodeModelById(edge.targetNodeId);
          return targetNode.properties.nodeType !== "TIMEOUT";
        });

        if (outgoingEdges.length > 1) {
          lf.deleteEdge(data.data.id);
          context.$message.warning(
            "普通节点只能有一条出边，并行请使用并行节点"
          );
          return;
        }
      }

      // 原有的边类型处理逻辑
      const nodeType = sourceNode.properties?.nodeType;
      if (nodeType) {
        const nodeConfig = nodeTypes.el.find(
          (item) => item.nodeType === nodeType
        );
        if (nodeConfig?.edgeTags?.length > 0) {
          let defaultEdgeType = "";
          const nodeEdges = lf.getNodeEdges(sourceNode.id);
          const outgoingEdges = nodeEdges.filter(
            (edge) => edge.sourceNodeId === sourceNode.id
          );
          const currentEdgeCount = outgoingEdges.length - 1;
          defaultEdgeType = nodeConfig.edgeTags[currentEdgeCount] || "";
          lf.setProperties(data.data.id, {
            edgeType: defaultEdgeType,
          });
          lf.updateText(data.data.id, defaultEdgeType);
        }
      }

      // 结束节点特殊处理
      if (targetNodeType === "END") {
        // 查找最近的循环或并行节点
        const nearestNode = findNearestNodeByType({
          id: data.data.targetNodeId,
          flowData: getFlowData(),
        });
        // 设置节点属性
        lf.setProperties(data.data.targetNodeId, {
          nearestNodeId: nearestNode?.id,
          nearestNodeType: nearestNode?.type,
        });
        lf.updateText(data.data.id, "end");
      }
    });

    // 选择事件
    lf.on("selection:selected", () => {
      context.hasSelected = true;
    });

    lf.on("selection:removed", () => {
      context.hasSelected = false;
    });

    // 边线事件
    lf.on("edge:click", ({ data }) => handleEdgeEvent(data));
    lf.on("edge:dbclick", ({ data }) => handleEdgeEvent(data));

    // 节点删除事件
    lf.on("node:delete", ({ data }) => {
      if (data.type === "start-node") {
        context.$message.warning("开始节点不能删除");
        lf.render(context.graphData);
        return false;
      }
      context.graphData = lf.getGraphData();
      return true;
    });

    // 删除前验证
    lf.on("node:delete:before", ({ data }) => {
      return data.type !== "start-node";
    });
  };

  // 设置选择模式配置
  lf.extension.selectionSelect.setSelectionSense((elements) => {
    return {
      nodes: elements.nodes.filter((node) => node.type !== "start-node"),
      edges: elements.edges,
    };
  });

  // 初始化事件监听
  setupEventListeners();

  // 配置控制面板
  lf.extension.control.addItem({
    key: "back",
    text: "返回上一页",
    icon: true,
    iconClass: "custom-back-icon",
    onClick: () => {
      if (context.isNodeCountChanged()) {
        context
          .$confirm("当前流程图节点数量有未保存的修改，是否确认离开?", "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning",
          })
          .then(() => {
            context.$router.go(-1);
          })
          .catch(() => {
            // 用户取消
          });
      } else {
        context.$router.go(-1);
      }
    },
  });

  lf.extension.control.addItem({
    key: "save",
    icon: true,
    text: "保存",
    iconClass: "custom-save-icon",
    onClick: () => {
      try {
        context.saveFlowData();
      } catch (error) {
        context.$message.error(error.message);
      }
    },
  });

  return lf;
}
