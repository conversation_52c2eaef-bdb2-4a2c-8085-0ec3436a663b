<template>
  <div class="rule-chain-details">
    <div class="page-header">
      <div class="header-left">
        <el-button
          icon="el-icon-arrow-left"
          @click="handleBack"
          type="text"
          class="back-btn"
        >
          返回列表
        </el-button>
        <div class="chain-info">
          <h2>{{ chainInfo.chainName }}</h2>
          <div class="chain-meta">
            <span>ID: {{ chainInfo.chainId }}</span>
            <span>版本: {{ chainInfo.version }}</span>
            <span>状态: {{ chainInfo.status === "1" ? "启用" : "停用" }}</span>
          </div>
        </div>
      </div>
      <div class="header-right">
        <el-button
          icon="el-icon-document"
          @click="handleSave"
          type="primary"
          :loading="saving"
        >
          保存配置
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <node-flow
        :defaultData="flowData"
        :chainId="chainId"
        @save="handleFlowSave"
        @debug-status-change="handleDebugStatusChange"
        @restore="handleRestore"
        @jumpToSubChain="handleJumpToSubChain"
      />
    </div>
  </div>
</template>

<script>
import NodeFlow from "./components/NodeFlow/index.vue";
import {
  getRuleChainDetail,
  updateRuleChain,
  addRuleChain,
} from "@/api/ruleEngine/index.js";

export default {
  name: "RuleChainDetails",
  components: {
    NodeFlow,
  },
  data() {
    return {
      chainId: "",
      saving: false,
      chainInfo: {
        chainId: "",
        chainName: "",
        version: "",
        status: "",
        description: "",
      },
      flowData: {
        chainId: "",
        chainName: "",
        version: "",
        expression: "",
        nodes: [],
        edges: [],
      },
      formData: {
        chainName: "",
        chainDesc: "",
      },
      isDebug: false,
    };
  },
  async activated() {
    // 从localStorage读取调试状态
    this.isDebug = localStorage.getItem("isDebug") === "true";

    const query = this.$route.query;
    if (query.chainId) {
      // 编辑模式
      this.chainId = query.chainId;
      await this.loadChainData();
    } else {
      // 新增模式
      this.flowData = {
        chainId: "",
        chainName: "",
        version: "",
        expression: "",
        nodes: [],
        edges: [],
      };
    }
  },
  methods: {
    // 加载规则链数据
    async loadChainData() {
      if (!this.chainId) return;
      this.saving = true;
      try {
        const res = await getRuleChainDetail({
          id: this.chainId,
        });
        if (res?.success) {
          this.flowData = res.data;
          this.chainInfo = {
            chainId: res.data.chainId,
            chainName: res.data.chainName,
            version: res.data.version,
            status: res.data.enable ? "1" : "0",
            description: res.data.chainDesc,
          };
          this.formData = {
            chainName: res.data?.chainName,
            chainDesc: res.data?.chainDesc,
          };
        }
      } catch (error) {
        this.$message.error("加载规则链数据失败");
      } finally {
        this.saving = false;
      }
    },

    // 流程图保存 - 主要保存方法
    async handleFlowSave({ nodes, edges, expression }) {
      if (this.chainId) {
        // 编辑模式 - 询问是否生成历史版本
        this.$confirm({
          title: "提示",
          content: "是否要生成历史版本?",
          okText: "是",
          cancelText: "否",
          onOk: async () => {
            await this.saveRuleChain(nodes, edges, expression, true);
          },
          onCancel: async () => {
            await this.saveRuleChain(nodes, edges, expression, false);
          },
        });
      } else {
        // 新增模式 - 需要先输入规则链名称
        if (!this.formData.chainName?.trim()) {
          this.$message.warning("请先输入规则链名称");
          return;
        }
        await this.saveRuleChain(nodes, edges, expression, false);
      }
    },

    // 保存规则链数据
    async saveRuleChain(nodes, edges, expression, isHistory) {
      this.saving = true;
      try {
        if (this.chainId) {
          // 更新现有规则链
          const res = await updateRuleChain({
            chainId: this.chainId,
            chainName: this.formData.chainName,
            chainDesc: this.formData.chainDesc,
            nodes,
            edges,
            expression,
            isHistory,
          });

          // 更新版本号并关闭调试模式
          if (res.data?.version) {
            this.flowData = {
              ...this.flowData,
              version: res.data.version,
            };
          }
          // 关闭调试模式
          localStorage.setItem("isDebug", "false");
          this.$message.success(
            isHistory ? "保存成功,并生成历史版本" : "保存成功"
          );
        } else {
          // 新增规则链
          const res = await addRuleChain({
            chainName: this.formData.chainName,
            chainDesc: this.formData.chainDesc,
            nodes,
            edges,
            expression,
          });
          this.$message.success("保存成功");
          this.$router.back();
        }
      } catch (error) {
        // 错误已在拦截器中处理
      } finally {
        this.saving = false;
      }
    },

    // 保存配置 - 兼容原有接口
    async handleSave() {
      // 这个方法现在主要用于工具栏的保存按钮
      const flowData = this.$refs.nodeFlow?.getFlowData();
      if (flowData) {
        await this.handleFlowSave(flowData);
      }
    },

    // 调试状态变化
    handleDebugStatusChange(isDebug) {
      console.log("调试状态变化:", isDebug);
      if (isDebug) {
        this.$message.info("已进入调试模式");
      } else {
        this.$message.info("已退出调试模式");
      }
    },

    // 恢复版本
    handleRestore(versionData) {
      console.log("恢复版本:", versionData);
      this.$message.success(`已恢复到版本 ${versionData.version}`);
      this.loadChainData();
    },

    // 跳转到子规则链
    handleJumpToSubChain(subChainId) {
      console.log("跳转到子规则链:", subChainId);
      this.$router.push({
        path: "/rule-engine/details",
        query: { chainId: subChainId },
      });
    },

    // 返回列表
    handleBack() {
      this.$router.push("/rule-engine");
    },
  },
};
</script>

<style lang="less" scoped>
.rule-chain-details {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 24px;
    background: #fff;
    border-bottom: 1px solid #e8e8e8;

    .header-left {
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 16px;
        font-size: 14px;
      }

      .chain-info {
        h2 {
          margin: 0 0 4px 0;
          font-size: 20px;
          font-weight: 500;
          color: #333;
        }

        .chain-meta {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #666;

          span {
            padding: 2px 8px;
            background: #f5f5f5;
            border-radius: 4px;
          }
        }
      }
    }

    .header-right {
      display: flex;
      gap: 8px;
    }
  }

  .page-content {
    flex: 1;
    overflow: hidden;
  }
}
</style>
