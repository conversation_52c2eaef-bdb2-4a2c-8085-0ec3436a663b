<template>
  <div class="card-container">
    <BuseCrud
      ref="crud"
      :loading="loading"
      :filterOptions="filterOptions"
      :tablePage="tablePage"
      :pagerProps="pagerProps"
      :tableColumn="tableColumn"
      :tableData="tableData"
      :tableProps="tableProps"
      :modalConfig="modalConfig"
      @modalConfirm="modalConfirmHandler"
      @rowDel="deleteRowHandler"
      @rowEdit="rowEdit"
      @loadData="loadData"
    >
      <template #toolbar_buttons>
        <el-button icon="el-icon-plus" type="primary" @click="showCreateModal">
          新增规则链
        </el-button>
      </template>

      <template slot="filterCustomBtn">
        <div class="btn-wrap">
          <el-button
            type="primary"
            icon="el-icon-download"
            @click.stop="handleExport"
          >
            导出
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-search"
            @click.stop="handleQuery"
            :loading="loading"
          >
            查询
          </el-button>
          <el-button
            icon="el-icon-refresh"
            @click.stop="handleReset"
            :loading="loading"
          >
            重置
          </el-button>
        </div>
      </template>

      <!-- 规则链名称插槽 -->
      <template #chainName="{ row }">
        <a
          href="javascript:void(0);"
          @click="handleConfig(row)"
          class="link-text"
        >
          {{ row.chainName }}
        </a>
      </template>

      <!-- 状态插槽 -->
      <template #enable="{ row }">
        <el-switch
          v-model="row.enable"
          :disabled="true"
          active-text="已启用"
          inactive-text="未启用"
        />
      </template>

      <!-- 筛选状态插槽 -->
      <template #enableFilter>
        <el-select
          v-model="params.enable"
          placeholder="请选择启用状态"
          clearable
        >
          <el-option label="已启用" :value="true" />
          <el-option label="未启用" :value="false" />
        </el-select>
      </template>
    </BuseCrud>
  </div>
</template>

<script>
import {
  getRuleChainList,
  updateRuleChainEnable,
  addRuleChain,
  updateRuleChain,
  getRuleChainDetail,
  deleteRuleChain,
} from "@/api/ruleEngine/index.js";

export default {
  name: "RuleChainList",
  data() {
    return {
      loading: false,
      tableData: [],
      params: {
        pageNum: 1,
        pageSize: 10,
        chainName: "",
        enable: undefined,
      },
      total: 0,
    };
  },
  computed: {
    // 表格列配置
    tableColumn() {
      return [
        { title: "序号", type: "seq", align: "center", width: 60 },
        {
          title: "规则名称",
          field: "chainName",
          align: "center",
          minWidth: "120px",
          slots: { default: "chainName" },
        },
        {
          title: "规则描述",
          field: "chainDesc",
          align: "center",
          maxWidth: "200px",
        },
        {
          title: "规则链版本",
          field: "version",
          align: "center",
        },
        {
          title: "更新时间",
          field: "updateTime",
          align: "center",
        },
        {
          title: "创建人",
          field: "createUser",
          align: "center",
        },
        {
          title: "状态",
          field: "enable",
          slots: { default: "enable" },
          align: "center",
          width: "80px",
        },
      ];
    },

    // 筛选配置
    filterOptions() {
      return {
        config: [
          {
            field: "chainName",
            title: "规则名称",
            defaultValue: undefined,
          },
          {
            field: "enable",
            title: "状态",
            element: "slot",
            slotName: "enableFilter",
            defaultValue: undefined,
          },
        ],
        params: this.params,
        gridCol: { xs: 8, sm: 8, md: 8, lg: 8, xl: 8, xxl: 8 },
      };
    },

    // 分页配置
    tablePage() {
      return {
        total: this.total,
        currentPage: this.params.pageNum,
        pageSize: this.params.pageSize,
      };
    },

    // 分页属性
    pagerProps() {
      return {
        pageSizes: [10, 20, 50, 100],
        layout: "total, sizes, prev, pager, next, jumper",
      };
    },

    // 表格属性
    tableProps() {
      return {
        border: true,
        align: "center",
        resizable: true,
        showOverflow: "tooltip",
        toolbarConfig: {
          custom: true,
          slots: {
            buttons: "toolbar_buttons",
          },
        },
        rowConfig: {
          keyField: "agingId",
          isCurrent: true,
        },
        checkboxConfig: {
          // checkRowKeys: selectRowsId,
          reserve: true,
        },
      };
    },

    // 弹窗配置
    modalConfig() {
      return {
        menu: true,
        menuWidth: 300,
        menuFixed: "right",
        addBtn: false,
        editBtn: true,
        editTitle: "编辑",
        delBtn: true,
        viewBtn: false,
        modalWidth: "600px",
        formConfig: [
          {
            field: "chainName",
            title: "规则链名称",
            rules: [
              { required: true, message: "请输入规则链名称", trigger: "blur" },
              { max: 32, message: "规则链名称最多32个字符", trigger: "blur" },
            ],
          },
          {
            field: "chainDesc",
            title: "规则链描述",
            element: "el-input",
            props: {
              type: "textarea",
              rows: 3,
            },
            rules: [
              { max: 64, message: "规则链描述最多64个字符", trigger: "blur" },
            ],
          },
        ],
        customOperationTypes: [
          {
            title: "配置",
            typeName: "config",
            event: (row) => {
              return this.handleConfig(row);
            },
            condition: () => {
              return true;
            },
          },
          {
            title: "启用",
            typeName: "enable",
            event: (row) => {
              return this.handleChainStatus(row, true);
            },
            condition: (row) => {
              return !row.enable;
            },
          },
          {
            title: "禁用",
            typeName: "disable",
            event: (row) => {
              return this.handleChainStatus(row, false);
            },
            condition: (row) => {
              return row.enable;
            },
          },
          {
            title: "复制",
            typeName: "copy",
            event: (row) => {
              return this.handleCopy(row);
            },
            condition: () => {
              return true;
            },
          },
        ],
      };
    },
  },
  created() {
    this.loadData();
  },
  methods: {
    // 加载数据
    async loadData() {
      this.loading = true;
      try {
        // 调用API获取规则链列表
        const res = await getRuleChainList({
          ...this.params,
          pageNum: this.params.pageNum,
          pageSize: this.params.pageSize,
        });

        this.total = res?.total || 0;
        this.tableData = res?.data || [];
      } catch (error) {
        this.$message.error("加载数据失败");
      } finally {
        this.loading = false;
      }
    },

    // 新增规则链 - 直接跳转到详情页
    showCreateModal() {
      localStorage.setItem("isDebug", "false");
      this.$router.push({
        name: "rule-engine-details",
      });
    },

    // 编辑规则链
    rowEdit(row) {
      this.$refs.crud.switchModalView(true, "edit", row);
    },

    // 配置规则链 - 跳转到详情页
    handleConfig(row) {
      localStorage.setItem("isDebug", "false");
      this.$router.push({
        name: "rule-engine-details",
        query: {
          chainId: row.chainId,
        },
      });
    },

    // 启用/禁用规则链
    async handleChainStatus(row, enable) {
      const action = enable ? "启用" : "禁用";
      await this.$confirm({
        title: "提示",
        content: `确定要${action}规则链"${row.chainName}"吗？`,
        onOk: async () => {
          try {
            await updateRuleChainEnable({
              id: row.chainId,
              enable,
            });
            this.$message.success(`${action}成功`);
            this.loadData();
          } catch (error) {
            // 错误已在拦截器中处理
          }
        },
      });
    },

    // 复制规则链
    async handleCopy(row) {
      // 先确认是否要复制
      this.$confirm({
        title: "复制规则链",
        content: `确定要复制规则链"${row.chainName}"吗？`,
        okText: "确定",
        cancelText: "取消",
        onOk: async () => {
          try {
            const detailRes = await getRuleChainDetail({
              id: row.chainId,
            });

            const copyData = {
              ...detailRes.data,
              chainId: "",
              chainName: `${detailRes.data.chainName}-副本`,
              enable: false,
            };

            const addRes = await addRuleChain(copyData);

            if (addRes && addRes.data.chainId) {
              this.$message.success("复制成功");
              this.loadData();

              // 弹框提示是否跳转详情
              this.$confirm({
                title: "复制成功",
                content: "是否跳转到详情页进行编辑?",
                okText: "确定",
                cancelText: "取消",
                onOk: () => {
                  localStorage.setItem("isDebug", "false");
                  this.$router.push({
                    name: "rule-engine-details",
                    query: {
                      chainId: addRes.data.chainId,
                    },
                  });
                },
              });
            } else {
              this.$message.error("复制失败");
            }
          } catch (error) {
            // 错误已在拦截器中处理
          }
        },
      });
    },

    // 删除规则链
    async deleteRowHandler(row) {
      if (row.enable) {
        this.$message.warning("启用状态的规则链不能删除，请先禁用");
        return;
      }
      await this.$confirm({
        title: "提示",
        content: `确定要删除规则链"${row.chainName}"吗？`,
        onOk: async () => {
          try {
            await deleteRuleChain({
              id: row.chainId,
            });
            this.$message.success("删除成功");
            this.loadData();
          } catch (error) {
            // 错误已在拦截器中处理
          }
        },
      });
    },

    // 弹窗确认 - 编辑规则链
    async modalConfirmHandler(formData) {
      try {
        await updateRuleChain({
          chainId: formData.chainId,
          chainName: formData.chainName,
          chainDesc: formData.chainDesc,
        });
        this.$message.success("更新成功");
        this.loadData();
      } catch (error) {
        // 错误已在拦截器中处理
      }
    },

    // 查询
    handleQuery() {
      this.params.pageNum = 1;
      this.loadData();
    },

    // 重置
    handleReset() {
      this.params = {
        pageNum: 1,
        pageSize: 10,
        chainName: undefined,
        enable: undefined,
      };
      this.loadData();
    },

    // 导出
    handleExport() {
      this.$message.info("导出功能开发中");
    },
  },
};
</script>

<style lang="less" scoped>
.btn-wrap {
  display: flex;
  gap: 8px;
}

.link-text {
  color: #1890ff;
  text-decoration: none;

  &:hover {
    color: #40a9ff;
    text-decoration: underline;
  }
}

.operate-button {
  color: #1890ff;
  cursor: pointer;
  margin-right: 16px;

  &:hover {
    color: #40a9ff;
  }

  &.color-error {
    color: #ff4d4f;

    &:hover {
      color: #ff7875;
    }
  }
}
</style>
